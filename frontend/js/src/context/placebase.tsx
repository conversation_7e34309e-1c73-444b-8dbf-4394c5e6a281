// Provider for browser location to state mapping context
/** @jsx h */

import {createContext, h} from 'preact';
import {useState} from 'preact/hooks';

import useEnsuredContext from '../hooks/useEnsuredContext';
import useUrl from '../hooks/useUrl';
import {PREFIXES} from '../rdf';

import type {Url, UrlNavigate} from '../hooks/useUrl';
import type {ComponentChildren, Hint, RNode} from '../types';

export type PlaceBase = {
  view: string | null;
  selection: string | null;
};

type Props = Readonly<{
  base: string;
  children: ComponentChildren;
}>;

export const PlaceUrlContext = createContext<[Url, Hint] | null>(null);
export const PlaceNavContext = createContext<[UrlNavigate, (hint: Hint) => void] | null>(null);
export const PlaceBaseContext = createContext<string>('');

export function PlaceProvider({base, children}: Props): RNode {
  const [hint, setHint] = useState<Hint>({name: 'load'});
  // TODO: validate base? must end with '/' maybe?
  const [url, nav] = useUrl(base);

  return (
    <PlaceBaseContext.Provider value={base}>
      <PlaceUrlContext.Provider value={[url, hint]}>
        <PlaceNavContext.Provider value={[nav, setHint]}>
          {children}
        </PlaceNavContext.Provider>
      </PlaceUrlContext.Provider>
    </PlaceBaseContext.Provider>
  );
}

export function viewFromPath(path: string) : string|null {
  if (path.length) {
    return PREFIXES.vm + decodeURI(path);
  }
  return null;
}


export function fromQualified(qname: string): string {
  // TODO: Use regexp matching
  for (const prefix of Object.keys(PREFIXES) as (keyof typeof PREFIXES)[]) {
    if (qname.startsWith(prefix + ':')) {
      return PREFIXES[prefix] + decodeURI(qname.slice(prefix.length + 1));
    }
  }
  return PREFIXES.vm + decodeURI(qname);
}

export function toQualified(iri: string): string {
  // TODO: Use regexp matching
  const keys = Object.keys(PREFIXES).filter(p => p !== 'vm') as (keyof typeof PREFIXES)[];
  for (const prefix of keys) {
    const lead = PREFIXES[prefix];
    if (iri.startsWith(lead)) {
      return prefix + ':' + decodeURI(iri.slice(lead.length));
    }
  }
  return encodeURI(iri.slice(PREFIXES.vm.length));
}

export function selectionFromHash(hash: string) : string|null {
  if (hash.length > 1) {
    return fromQualified(hash);
  }
  return null;
}

export function usePlaceBase(): PlaceBase {
  const [url, _] = useEnsuredContext(PlaceUrlContext);
  const view = viewFromPath(url.path);
  const selection = selectionFromHash(url.hash);

  return {view, selection};
}
