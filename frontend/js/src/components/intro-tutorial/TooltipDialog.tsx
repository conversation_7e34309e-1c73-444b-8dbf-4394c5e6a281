/** @jsx h */
import {h} from 'preact';
import {useEffect, useState} from 'preact/hooks';

import Button from '../Button';
import {Translate, useTranslation} from '../../intl';

import type {RNode} from '../../types';
import type {TutorialStep} from './types';

interface TooltipDialogProps {
  currentStep: number;
  steps: TutorialStep[];
  targetRect: DOMRect | null;
  tooltipLeft: number;
  tooltipPosition: 'top' | 'bottom' | 'right';
  isLargeScreen: boolean;
  progress: number;
  onExit: () => void;
  onPrev: () => void;
  onNext: () => void;
}

function getTooltipPositionStyles(
  isLargeScreen: boolean,
  tooltipPosition: 'top' | 'bottom' | 'right',
  targetRect: DOMRect | null,
  tooltipLeft: number,
  currentStepPushUpMobile: number,
) {
  if (isLargeScreen) {
    let top: string | undefined;
    let left: string | undefined;
    let maxWidth: string | undefined;
    const transform: string | undefined = 'translateY(0)';
    if (tooltipPosition === 'top') {
      top = `calc(${(targetRect?.top ?? 0) + window.scrollY}px - 13.75rem)`;
      left = `${tooltipLeft}rem`;
    } else if (tooltipPosition === 'bottom') {
      top = `calc(${(targetRect?.bottom ?? 0) + window.scrollY}px + 2.5rem)`;
      left = `${tooltipLeft}rem`;
    } else if (tooltipPosition === 'right') {
      top = `${(targetRect?.top ?? 0) + window.scrollY}px`;
      const bufferRem = 2.5;
      left = `calc(${(targetRect?.right ?? 0) + window.scrollX}px + ${bufferRem}rem)`;
      maxWidth = `calc(100vw - ${(targetRect?.right ?? 0) + window.scrollX}px - ${bufferRem}rem - var(--panel-spacing))`;
    }
    return {top, left, maxWidth, transform};
  }
  return {
    top: `calc(50% - ${currentStepPushUpMobile}px)`,
    left: '50%',
    transform: 'translate(-50%, -50%)',
  };

}

export default function TooltipDialog({
  currentStep,
  steps,
  targetRect,
  tooltipLeft,
  tooltipPosition,
  isLargeScreen,
  progress,
  onExit,
  onPrev,
  onNext,
}: TooltipDialogProps): RNode {
  const intl = useTranslation();
  const [shouldAnnounce, setShouldAnnounce] = useState(false);

  // Handle first step announcement with a timer
  useEffect(() => {
    setShouldAnnounce(false);

    // Small delay to ensure the DOM is fully rendered and screen reader catches the change
    const timer = setTimeout(() => {
      setShouldAnnounce(true);
    }, 100);

    return () => clearTimeout(timer);
  }, [currentStep]);

  const currentStepPushUpMobile = steps[currentStep]?.pushUpMobile || 0;

  const tooltipStyles = getTooltipPositionStyles(
    isLargeScreen,
    tooltipPosition,
    targetRect,
    tooltipLeft,
    currentStepPushUpMobile,
  );

  return (
    <div
      className="vm-intro-tooltip"
      style={tooltipStyles}
    >
      {/*
        Screen reader announcement - uses a timer-based approach to ensure all steps are announced
        The content is initially empty, then populated after a short delay to ensure
        screen readers detect the change and announce it properly
      */}
      <div className="vm-sr-only" aria-live="assertive">
        {shouldAnnounce
          ? <Translate
              defaultMessage="Tutorial Step {currentStep} of {totalSteps}. {highlightingText} {description}"
              values={{
                currentStep: currentStep + 1,
                totalSteps: steps.length,
                highlightingText: steps[currentStep].ariaLabel ? intl.translate({defaultMessage: 'Highlighting: {element}.'}, {element: steps[currentStep].ariaLabel}) : '',
                description: steps[currentStep].description,
              }}
            /> : ''}
      </div>
      <div className="vm-intro-tooltip-text">
        {steps[currentStep].description}
      </div>

      <div className="vm-intro-tooltip-buttons">
        <Button
          className="vm-primary-button vm-toggle-button"
          onClick={onExit}
          data-testid="intro-tutorial-exit-button"
        >
          <Translate defaultMessage="Exit" />
        </Button>
        <Button
          className="vm-primary-button vm-toggle-button"
          onClick={onPrev}
          disabled={currentStep === 0}
          data-testid="intro-tutorial-previous-button"
        >
          <Translate defaultMessage="Previous" />
        </Button>
        <Button
          className="vm-primary-button vm-toggle-button"
          onClick={onNext}
          data-testid={currentStep === steps.length - 1 ? 'intro-tutorial-finish-button' : 'intro-tutorial-next-button'}
        >
          {currentStep === steps.length - 1
            ? <Translate defaultMessage="Finish" />
            : <Translate defaultMessage="Next" />}
        </Button>
      </div>

      <div className="vm-intro-tooltip-step">
        <Translate
          defaultMessage="Step {current} of {total}"
          values={{
            current: <span data-testid="intro-tutorial-current-step">{currentStep + 1}</span>,
            total: <span data-testid="intro-tutorial-total-steps">{steps.length}</span>,
          }}
        />
      </div>

      <div className="vm-intro-progress-container">
        <div
          className="vm-intro-progress-bar"
          style={{width: `${progress}%`}}
        />
      </div>
    </div>
  );
}
