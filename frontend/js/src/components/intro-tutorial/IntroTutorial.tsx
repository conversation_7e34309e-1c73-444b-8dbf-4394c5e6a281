/** @jsx h */
import {h} from 'preact';
import {useCallback, useEffect, useLayoutEffect, useRef, useState} from 'preact/hooks';

import {useScreenProperties} from '../../context/screen';
import {useTutorial} from '../../context/tutorial';
import {usePlatformStates} from '../../context/platform';
import Backdrop from './Backdrop';
import ConfettiEffect from './ConfettiEffect';
import CongratulationsDialog from './CongratulationsDialog';
import HighlightedElement from './HighlightedElement';
import {localStorageTutorialCompleted, localStorageTutorialNotCompleted, shouldShowTutorial} from './localStorage';
import TooltipDialog from './TooltipDialog';
import WelcomeScreen from './WelcomeScreen';

import type {RNode} from '../../types';

/**
 * Interactive tutorial component that guides users through the application's features.
 *
 * The IntroTutorial component manages a step-by-step walkthrough of the application,
 * highlighting different UI elements and providing explanatory tooltips. It includes:
 *
 * - A welcome screen with options to start, skip, or postpone the tutorial
 * - Step-by-step navigation through important UI elements
 * - Highlighted elements with tooltips explaining their functionality
 * - Keyboard navigation support (arrow keys and Escape)
 * - A congratulations screen with confetti effect upon completion
 * - Persistence of tutorial completion status in localStorage
 * - Responsive design that adapts to different screen sizes
 *
 * The tutorial flow has several states:
 * - `-1`: Welcome screen
 * - `0` to `steps.length-1`: Tutorial steps
 * - `-2`: Congratulations screen
 *
 * @returns {RNode} The rendered tutorial component
 */
export default function IntroTutorial(): RNode | null {
  const {validSteps: steps, completeTutorial, forceTutorial} = useTutorial();
  const [currentStep, setCurrentStep] = useState(-1);
  const [isVisible, setIsVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const {isLargeScreen} = useScreenProperties();
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const {base: baseReady} = usePlatformStates();

  // References to elements that should be made inert during tutorial
  const inertElementsRef = useRef<HTMLElement[]>([]);

  /**
   * Helper function to remove inert attributes from all tracked elements
   * and clear the reference array.
   */
  const removeInertAttributes = useCallback(() => {
    inertElementsRef.current.forEach(element => {
      element.removeAttribute('inert');
    });
    inertElementsRef.current = [];
  }, []);

  /**
   * Advances to the next tutorial step or shows the congratulations screen if on the last step.
   * When reaching the last step, it triggers the confetti effect and focuses the completion button.
   */
  const nextStep = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else if (currentStep === steps.length - 1) {
      // Show the "Congratulations" dialog on the last step
      setShowConfetti(true);
      setShowCongratulations(true);
      setCurrentStep(-2); // Hide the tooltip
      setTargetRect(null); // Immediately clear the highlighted element
      setTimeout(() => {
        setShowConfetti(false);
      }, 4000);
    }
  }, [currentStep, steps.length]);

  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  /**
   * Exits the tutorial without completing it.
   * Used for the "Exit" button during the tutorial, allowing users to exit
   * but see the tutorial again on their next visit.
   */
  const exitTutorial = useCallback(() => {
    localStorageTutorialNotCompleted();
    setIsVisible(false);
    removeInertAttributes();
    completeTutorial();
  }, [completeTutorial, removeInertAttributes]);

  /**
   * Exits the tutorial and marks it as completed in the localStorage.
   * Used for the "Skip Tutorial" option, preventing the tutorial from showing again
   * unless the application is updated with a new tutorial version.
   */
  const exitTutorialComplete = useCallback(() => {
    // Mark tutorial as completed in localStorage
    localStorageTutorialCompleted();
    setIsVisible(false);
    removeInertAttributes();

    completeTutorial();
  }, [completeTutorial, removeInertAttributes]);

  // Add keyboard navigation for left, right, and Esc keys
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        exitTutorial();
      }

      // Prevent arrrow keys step navigation on Welcome and Congratulation screens
      if (currentStep === -1 || currentStep === -2) {
        return;
      }

      if (event.key === 'ArrowRight') {
        nextStep();
      } else if (event.key === 'ArrowLeft') {
        prevStep();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentStep, exitTutorial, nextStep, prevStep, steps.length]); // Re-run effect when currentStep changes

  // Function to update target element position
  const updateTargetPosition = useCallback(() => {
    if (currentStep >= 0) {
      const step = steps[currentStep];
      const targetElement = document.getElementById(step?.elementId);
      setTargetRect(targetElement?.getBoundingClientRect() ?? null);
    } else if (currentStep === -2) {
      // Clear the target rectangle when showing the congratulations dialog
      setTargetRect(null);
    }
  }, [currentStep, steps]);

  // Update position when window is resized
  useEffect(() => {
    const handleResize = () => {
      if (currentStep !== -2) {
        // Only update the target position if not on the "Congratulations" stage
        updateTargetPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [currentStep, updateTargetPosition]);

  // Update position when step changes
  useEffect(() => {
    updateTargetPosition();
  }, [currentStep, steps, updateTargetPosition]);

  // Reset current step to 0 if steps change
  useEffect(() => {
    if (currentStep === -1 || currentStep === -2) {
      // Do not reset if on the initial welcome screen or "Congratulations" stage
      return;
    }
    setCurrentStep(0);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [steps]);

  // Initialize tutorial based on localStorage state
  useEffect(() => {
    // If forceShow is true, always show the tutorial
    // Otherwise, check localStorage to see if we should show it
    const shouldShow = forceTutorial || shouldShowTutorial();

    if (shouldShow) {
      setIsVisible(true);
    } else {
      completeTutorial();
    }
  }, [forceTutorial, completeTutorial]);

  // Apply inert attribute to background elements when tutorial is visible and base UI is ready
  useLayoutEffect(() => {
    // Only apply inert attribute when tutorial is visible and base UI is ready
    if (!isVisible || !baseReady) {
      return function noop(): void {};
    }

    removeInertAttributes();

    // Find the vm-page element and make all its children inert except for the tutorial elements
    const vmPage = document.querySelector('.vm-page');

    if (vmPage) {
      const children = vmPage.children;

      // Loop through all children and make them inert if they're not part of the tutorial
      for (let i = 0; i < children.length; i++) {
        const child = children[i] as HTMLElement;

        // Skip the tutorial elements
        if (child.classList.contains('vm-intro-welcome') || child.classList.contains('vm-intro-tutorial')) {
          continue;
        }

        // Make all other elements inert
        child.setAttribute('inert', '');
        inertElementsRef.current.push(child);
      }
    }

    // Cleanup function to remove inert attribute and disconnect observer
    return function cleanup(): void {
      removeInertAttributes();
    };
  }, [isVisible, baseReady, removeInertAttributes]);

  // Progress percentage calculation
  const progress = currentStep >= 0 ? ((currentStep + 1) / steps.length) * 100 : 0;

  if (steps.length === 0) {
    return null;
  }

  // Handle welcome screen
  if (currentStep === -1) {
    return (
      <WelcomeScreen
        isVisible={isVisible}
        isLargeScreen={isLargeScreen}
        onExitLater={exitTutorial}
        onExit={exitTutorialComplete}
        onStart={nextStep}
      />
    );
  }

  const windowHeight = window.innerHeight;
  // Get the current font size to calculate rem values
  const htmlFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
  const pxToRem = (px: number) => px / htmlFontSize;

  // Tooltip width is 25rem as defined in CSS
  const tooltipWidthRem = 25;

  let tooltipLeftRem = 0;
  if (targetRect) {
    const targetLeftRem = pxToRem((targetRect.left ?? 0) + window.scrollX);
    tooltipLeftRem = targetLeftRem - tooltipWidthRem - pxToRem(50);

    // Prevent tooltip from going off the left edge
    const scrollXRem = pxToRem(window.scrollX);
    if (tooltipLeftRem < scrollXRem) {
      tooltipLeftRem = scrollXRem + 1.25;
    }
  }

  // Decide tooltip position: prefer bottom, then top, otherwise fallback to right if neither fits
  let tooltipPosition: 'top' | 'bottom' | 'right' = 'bottom';
  if (targetRect) {
    const spaceAbove = targetRect.top;
    const spaceBelow = windowHeight - targetRect.bottom;
    const tooltipHeight = 180; //for 2 line description
    const minSpace = tooltipHeight + 40; // add some buffer

    if (spaceBelow > minSpace) {
      tooltipPosition = 'bottom';
    } else if (spaceAbove > minSpace) {
      tooltipPosition = 'top';
    } else {
      tooltipPosition = 'right';
    }
  }

  // Handle congratulations dialog completion
  const handleCongratulationsComplete = () => {
    // Mark tutorial as completed in localStorage and exit
    localStorageTutorialCompleted();
    setShowCongratulations(false);
    removeInertAttributes();
    completeTutorial();
  };

  return (
    <div className="vm-intro-tutorial">
      {showConfetti && <ConfettiEffect />}

      {showCongratulations && (
        <CongratulationsDialog
          onComplete={handleCongratulationsComplete}
        />
      )}

      <Backdrop
        targetRect={targetRect}
        isCongratulationsScreen={currentStep === -2}
      />

      {targetRect && currentStep >= 0 && currentStep < steps.length && (
        <HighlightedElement
          targetRect={targetRect}
          stepId={steps[currentStep]?.elementId || `step-${currentStep}`}
        />
      )}

      {currentStep >= 0 && currentStep < steps.length && (
        <TooltipDialog
          currentStep={currentStep}
          steps={steps}
          targetRect={targetRect}
          tooltipLeft={tooltipLeftRem}
          tooltipPosition={tooltipPosition}
          isLargeScreen={isLargeScreen}
          progress={progress}
          onExit={exitTutorial}
          onPrev={prevStep}
          onNext={nextStep}
        />
      )}
    </div>
  );
}
