// Provides a control for managing tristate filters

/** @jsx h */
import {h} from 'preact';
import {useCallback, useMemo} from 'preact/hooks';
import type {Inclusion, RNode, TargetedEvent} from '../types';
import {InclusionFilterItem} from './InclusionFilterItem';
import {MultiselectDropdown} from './MultiselectDropdown';
import {localeInsensitive} from '../sort';
import {useTranslation} from '../intl';

import Button from './Button';

type InclusionProps = Readonly<{
  inclusion: Inclusion;
  heading: string;
  optionIris: string[];
  namer: (iri: string) => string;
  onChange: (newValue: Inclusion|undefined) => void;
}>;
export function InclusionFilterControl(props: InclusionProps): RNode|null {
  const {onChange, namer, optionIris, heading, inclusion} = props;
  const intl = useTranslation();

  const filterItemChange = useCallback((value: boolean|undefined, iri: string) => {
    let newInclusion: Inclusion|undefined = undefined;
    if (value != null) {
      newInclusion = {
        ...inclusion,
        [iri]: value,
      };
    } else {
      const {[iri]: _, ...rest} = inclusion ?? {};
      if (Object.keys(rest).length) {
        newInclusion = rest;
      }
    }

    onChange(newInclusion);
  }, [inclusion, onChange]);

  const filterItemAdd = useCallback((iri: string) => {
    filterItemChange(true, iri);
  }, [filterItemChange]);

  return useMemo(() => {
    const included = [];
    for (const iri in inclusion) {
      included.push({key: iri, name: namer(iri)});
    }

    if (optionIris.length === 0 && included.length === 0) {
      return null;
    }

    const excluded = optionIris.filter(iri => !inclusion || inclusion[iri] == null)
      .map(iri => ({key: iri, name: namer(iri)}));

    included.sort((a, b) => localeInsensitive(a.name, b.name));
    excluded.sort((a, b) => localeInsensitive(a.name, b.name));
    const clearFilter = (e: TargetedEvent<HTMLButtonElement, Event>) => {
      e.preventDefault();
      onChange(undefined);
    };

    const selectAll = (e: TargetedEvent<HTMLButtonElement, Event>) => {
      e.preventDefault();
      const newInclusion: Inclusion = {};
      optionIris.forEach(k => newInclusion[k] = true);
      onChange(newInclusion);
    };

    return <fieldset>
      <div className="pure-u-1 label-bar">
        <label>{heading}</label>
        {/* TODO: better buttons/icons */}
        <Button tooltipMessage={intl.translate({defaultMessage: 'Select All'})} onClick={selectAll}>&#10004;</Button>
        <Button tooltipMessage={intl.translate({defaultMessage: 'Clear All'})} onClick={clearFilter}>&#10006;</Button>
      </div>

      <MultiselectDropdown items={excluded} itemAdd={filterItemAdd} />

      <ul className="vm-filter-list">
        {included.map(item => <InclusionFilterItem
          key={item.key}
          iri={item.key}
          value={inclusion && inclusion[item.key]}
          label={item.name}
          onChange={filterItemChange} />)}
      </ul>

    </fieldset>;
  }, [optionIris, inclusion, heading, intl, filterItemAdd, namer, onChange, filterItemChange]);
}
